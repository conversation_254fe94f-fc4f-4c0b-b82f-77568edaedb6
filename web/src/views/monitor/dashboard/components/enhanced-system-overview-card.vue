<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { NCard, NStatistic, NTag, NButton, NSpace, NIcon, NGrid, NGridItem, NProgress } from 'naive-ui';
import { useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';


interface Props {
  healthData?: Api.Monitor.AppHealthData | null;
  systemData?: Api.Monitor.SystemOverviewData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  healthData: null,
  systemData: null,
  loading: false
});

const router = useRouter();



// 计算系统状态
const systemStatus = computed(() => {
  if (!props.healthData) {
    return { type: 'default', text: '未知', color: '#d9d9d9' };
  }

  const status = props.healthData.app_info.status;
  switch (status) {
    case 'healthy':
      return { type: 'success', text: '健康', color: '#52c41a' };
    case 'warning':
      return { type: 'warning', text: '警告', color: '#faad14' };
    case 'error':
      return { type: 'error', text: '错误', color: '#ff4d4f' };
    default:
      return { type: 'default', text: '未知', color: '#d9d9d9' };
  }
});

// 计算数据库状态
const dbStatus = computed(() => {
  if (!props.healthData?.database) {
    return { type: 'default', text: '未知', color: '#d9d9d9' };
  }

  const status = props.healthData.database.status;
  switch (status) {
    case 'connected':
      return { type: 'success', text: '已连接', color: '#52c41a' };
    case 'disconnected':
      return { type: 'error', text: '已断开', color: '#ff4d4f' };
    default:
      return { type: 'warning', text: '连接中', color: '#faad14' };
  }
});

// 计算运行时长
const uptimeText = computed(() => {
  if (!props.healthData?.app_info.uptime) return '0分钟';

  const uptime = props.healthData.app_info.uptime;
  const hours = Math.floor(uptime / 3600);
  const minutes = Math.floor((uptime % 3600) / 60);

  if (hours > 24) {
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    return `${days}天${remainingHours}小时`;
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else {
    return `${minutes}分钟`;
  }
});

// 计算应用程序CPU使用率
const appCpuUsage = computed(() => {
  if (!props.systemData?.available || !props.systemData.application) return 0;
  return props.systemData.application.cpu_percent || 0;
});

// 计算应用程序内存使用率（用于进度条显示）
const appMemoryUsage = computed(() => {
  if (!props.systemData?.available || !props.systemData.application) return 0;
  return Math.round(props.systemData.application.memory.percent || 0);
});

// 计算应用程序内存使用量（MB）
const appMemoryUsageMB = computed(() => {
  if (!props.systemData?.available || !props.systemData.application) return 0;
  return Math.round(props.systemData.application.memory.rss_mb || 0);
});

// 格式化内存大小
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 获取使用率状态
function getUsageStatus(usage: number) {
  if (usage <= 60) return { type: 'success', color: '#52c41a', text: '正常' };
  if (usage <= 80) return { type: 'warning', color: '#faad14', text: '警告' };
  return { type: 'error', color: '#ff4d4f', text: '危险' };
}



function goToSystemDetail() {
  router.push('/monitor/system');
}
</script>

<template>
  <NCard class="enhanced-system-overview-card">
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <Icon icon="mdi:monitor-dashboard" class="text-2xl text-blue-500" />
          <span class="text-lg font-semibold">系统状态概览</span>
        </div>
        <NButton text @click="goToSystemDetail">
          <template #icon>
            <Icon icon="mdi:arrow-right" />
          </template>
          查看详情
        </NButton>
      </div>
    </template>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 应用程序资源监控 -->
      <div class="app-resource-container">
        <div class="resource-header">
          <Icon icon="mdi:application" class="text-2xl text-purple-500" />
          <span class="resource-title">StreamForge 应用资源</span>
        </div>

        <!-- CPU和内存使用率卡片 -->
        <div class="resource-cards">
          <!-- CPU使用率卡片 -->
          <div class="resource-card cpu-card">
            <div class="card-header">
              <div class="icon-wrapper cpu-icon">
                <Icon icon="mdi:cpu-64-bit" class="text-lg" />
              </div>
              <div class="card-info">
                <div class="card-label">CPU使用率</div>
                <div class="card-value" :style="{ color: getUsageStatus(appCpuUsage).color }">
                  {{ appCpuUsage.toFixed(3) }}%
                </div>
              </div>
            </div>
            <div class="progress-container">
              <NProgress :percentage="appCpuUsage" :color="getUsageStatus(appCpuUsage).color" :show-indicator="false"
                :height="6" :border-radius="3" />
              <div class="status-badge" :class="getUsageStatus(appCpuUsage).type">
                {{ getUsageStatus(appCpuUsage).text }}
              </div>
            </div>
          </div>

          <!-- 内存使用量卡片 -->
          <div class="resource-card memory-card">
            <div class="card-header">
              <div class="icon-wrapper memory-icon">
                <Icon icon="mdi:memory" class="text-lg" />
              </div>
              <div class="card-info">
                <div class="card-label">内存使用量</div>
                <div class="card-value" :style="{ color: getUsageStatus(appMemoryUsage).color }">
                  {{ appMemoryUsageMB }}M
                </div>
              </div>
            </div>
            <div class="progress-container">
              <NProgress :percentage="appMemoryUsage" :color="getUsageStatus(appMemoryUsage).color"
                :show-indicator="false" :height="6" :border-radius="3" />
              <div class="status-badge" :class="getUsageStatus(appMemoryUsage).type">
                {{ getUsageStatus(appMemoryUsage).text }}
              </div>
            </div>
          </div>
        </div>

        <!-- 详细信息 -->
        <div v-if="systemData?.available && systemData.application" class="app-details">
          <div class="details-grid">
            <div class="detail-card">
              <Icon icon="mdi:identifier" class="detail-icon" />
              <div class="detail-content">
                <div class="detail-label">进程ID</div>
                <div class="detail-value">{{ systemData.application.pid }}</div>
              </div>
            </div>

            <div class="detail-card">
              <Icon icon="mdi:layers-triple" class="detail-icon" />
              <div class="detail-content">
                <div class="detail-label">线程数</div>
                <div class="detail-value">{{ systemData.application.threads }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统信息 -->
      <div class="system-info">
        <NGrid :cols="2" :x-gap="16" :y-gap="16">
          <!-- 系统状态 -->
          <NGridItem>
            <div class="info-item">
              <div class="info-icon">
                <Icon icon="mdi:server" :style="{ color: systemStatus.color }" />
              </div>
              <div class="info-content">
                <div class="info-label">系统状态</div>
                <NTag :type="systemStatus.type" size="large">
                  {{ systemStatus.text }}
                </NTag>
              </div>
            </div>
          </NGridItem>

          <!-- 数据库状态 -->
          <NGridItem>
            <div class="info-item">
              <div class="info-icon">
                <Icon icon="mdi:database" :style="{ color: dbStatus.color }" />
              </div>
              <div class="info-content">
                <div class="info-label">数据库</div>
                <NTag :type="dbStatus.type" size="large">
                  {{ dbStatus.text }}
                </NTag>
              </div>
            </div>
          </NGridItem>

          <!-- 运行时长 -->
          <NGridItem>
            <div class="info-item">
              <div class="info-icon">
                <Icon icon="mdi:clock-outline" class="text-green-500" />
              </div>
              <div class="info-content">
                <div class="info-label">运行时长</div>
                <div class="info-value">{{ uptimeText }}</div>
              </div>
            </div>
          </NGridItem>

          <!-- 平台信息 -->
          <NGridItem>
            <div class="info-item">
              <div class="info-icon">
                <Icon icon="mdi:desktop-tower" class="text-purple-500" />
              </div>
              <div class="info-content">
                <div class="info-label">运行平台</div>
                <div class="info-value">{{ healthData?.app_info.platform || '未知' }}</div>
              </div>
            </div>
          </NGridItem>
        </NGrid>

        <!-- 版本信息 -->
        <div class="version-info mt-4 p-3 bg-gray-50 rounded-lg">
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">应用版本</span>
            <span class="font-mono text-sm">{{ healthData?.app_info.version || 'v1.0.0' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="mt-6 pt-4 border-t border-gray-200">
      <NSpace justify="center">
        <NButton @click="goToSystemDetail" type="primary" ghost>
          <template #icon>
            <Icon icon="mdi:chart-line" />
          </template>
          查看系统详情
        </NButton>
      </NSpace>
    </div>
  </NCard>
</template>

<style scoped>
.enhanced-system-overview-card {
  height: 100%;
  transition: all 0.3s ease;
}

.enhanced-system-overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.app-resource-container {
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 20px;
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.app-resource-container:hover {
  transform: translateY(-2px);
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

.resource-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(139, 92, 246, 0.1);
}

.resource-title {
  font-size: 1.125rem;
  font-weight: 700;
  color: #374151;
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.resource-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.resource-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.resource-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.resource-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.resource-card:hover::before {
  opacity: 1;
}

.cpu-card::before {
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
}

.memory-card::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.cpu-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.memory-icon {
  background: linear-gradient(135deg, #10b981, #047857);
}

.card-info {
  flex: 1;
}

.card-label {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 2px;
}

.card-value {
  font-size: 1.5rem;
  font-weight: 700;
  transition: color 0.3s ease;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-badge {
  font-size: 0.625rem;
  padding: 2px 6px;
  border-radius: 6px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.success {
  background: #dcfce7;
  color: #166534;
}

.status-badge.warning {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.error {
  background: #fee2e2;
  color: #991b1b;
}

.app-details {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.detail-card {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.detail-card:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-1px);
}

.detail-icon {
  font-size: 1.25rem;
  color: #6366f1;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-content {
  flex: 1;
}

.detail-label {
  font-size: 0.625rem;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 2px;
}

.detail-value {
  font-size: 0.75rem;
  color: #374151;
  font-weight: 600;
}

.system-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.info-item:hover {
  background: #e2e8f0;
  transform: translateY(-1px);
}

.info-icon {
  font-size: 1.5rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: 0.75rem;
  color: #64748b;
  margin-bottom: 2px;
}

.info-value {
  font-weight: 600;
  color: #1e293b;
}

.version-info {
  border: 1px solid #e2e8f0;
}

/* 动画效果 */
.card-value {
  animation: fadeInUp 0.8s ease-out;
}

.status-badge {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.detail-card {
  animation: fadeInUp 0.6s ease-out;
}

.detail-card:nth-child(1) {
  animation-delay: 0.1s;
}

.detail-card:nth-child(2) {
  animation-delay: 0.2s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .grid {
    grid-template-columns: 1fr;
  }

  .resource-cards {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .details-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .app-resource-container {
    padding: 16px;
  }

  .resource-header {
    margin-bottom: 16px;
  }

  .resource-title {
    font-size: 1rem;
  }

  .resource-card {
    padding: 12px;
  }

  .card-value {
    font-size: 1.25rem;
  }

  .icon-wrapper {
    width: 32px;
    height: 32px;
  }

  .detail-card {
    padding: 8px;
  }

  .detail-label {
    font-size: 0.5rem;
  }

  .detail-value {
    font-size: 0.625rem;
  }
}
</style>
