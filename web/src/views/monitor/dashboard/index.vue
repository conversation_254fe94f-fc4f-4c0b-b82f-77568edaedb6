<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { NGrid, NGridItem, NButton, NSpace, NSpin, NAlert, NTag, NCard, NStatistic, NProgress } from 'naive-ui';
import { Icon } from '@iconify/vue';
import SystemOverviewCard from './components/enhanced-system-overview-card.vue';
import TaskOverviewCard from './components/enhanced-task-overview-card.vue';
import PerformanceOverviewCard from './components/enhanced-performance-overview-card.vue';
import ErrorOverviewCard from './components/enhanced-error-overview-card.vue';
import ResourceOverviewCard from './components/enhanced-resource-overview-card.vue';

import { useRealtimeData } from './composables/useRealtimeData';
import { fetchAppHealth, fetchBusinessStats, fetchPerformanceData, fetchSystemOverview } from '@/service/api';

// 监控数据状态
const loading = ref(false);
const error = ref<string | null>(null);

// 概览数据
const healthData = ref<Api.Monitor.AppHealthData | null>(null);
const businessData = ref<Api.Monitor.BusinessStatsData | null>(null);
const performanceData = ref<Api.Monitor.PerformanceData | null>(null);
const systemData = ref<Api.Monitor.SystemOverviewData | null>(null);

// SSE实时数据
const {
  isConnected,
  isConnecting,
  realtimeData,
  error: sseError,
  lastUpdateTime,
  connect,
  disconnect,
  reconnect,
  getConnectionStatus
} = useRealtimeData();

// 是否启用实时模式
const realtimeMode = ref(true);

// 自动刷新定时器（作为SSE的备用方案）
let refreshTimer: NodeJS.Timeout | null = null;

onMounted(async () => {
  try {
    // 首次加载概览数据
    await loadOverviewData();

    if (realtimeMode.value) {
      // 启动SSE实时连接
      connect();
    } else {
      // 启动定时器轮询（备用方案）
      startAutoRefresh();
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : '初始化监控页面失败';
  }
});

onUnmounted(() => {
  // 清理资源
  disconnect();
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
});

// 监听SSE数据变化，更新概览数据
watch(realtimeData, (newData) => {
  if (newData && !newData.error) {
    // 添加数据更新动画效果
    const cards = document.querySelectorAll('.n-card');
    cards.forEach((card, index) => {
      setTimeout(() => {
        card.classList.add('data-updated');
        setTimeout(() => {
          card.classList.remove('data-updated');
        }, 1000);
      }, index * 100);
    });

    // 平滑更新概览数据
    if (newData.health) {
      healthData.value = newData.health;
    }
    if (newData.business) {
      businessData.value = newData.business;
    }
    if (newData.performance) {
      performanceData.value = newData.performance;
    }
    if (newData.system) {
      systemData.value = newData.system;
    }
  }
});

// 监听SSE错误
watch(sseError, (newError) => {
  if (newError) {
    error.value = `实时连接错误: ${newError}`;
  }
});

function startAutoRefresh() {
  // 每30秒自动刷新一次（备用方案）
  refreshTimer = setInterval(() => {
    handleRefresh();
  }, 30000);
}

// 加载概览数据
async function loadOverviewData() {
  try {
    loading.value = true;
    error.value = null;

    // 并行加载所有概览数据
    const [healthRes, businessRes, performanceRes, systemRes] = await Promise.allSettled([
      fetchAppHealth(),
      fetchBusinessStats(),
      fetchPerformanceData(60),
      fetchSystemOverview()
    ]);

    // 处理结果
    if (healthRes.status === 'fulfilled') {
      healthData.value = healthRes.value.data;
    }
    if (businessRes.status === 'fulfilled') {
      businessData.value = businessRes.value.data;
    }
    if (performanceRes.status === 'fulfilled') {
      performanceData.value = performanceRes.value.data;
    }
    if (systemRes.status === 'fulfilled') {
      systemData.value = systemRes.value.data;
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载监控数据失败';
    console.error('加载监控概览数据失败:', err);
  } finally {
    loading.value = false;
  }
}

function handleRefresh() {
  // 刷新概览数据
  loadOverviewData();
}

function toggleRealtimeMode() {
  realtimeMode.value = !realtimeMode.value;

  if (realtimeMode.value) {
    // 切换到实时模式
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
    }
    connect();
  } else {
    // 切换到轮询模式
    disconnect();
    startAutoRefresh();
    handleRefresh(); // 立即刷新一次
  }
}

function handleManualRefresh() {
  if (realtimeMode.value) {
    // 实时模式下重连SSE
    reconnect();
  } else {
    // 轮询模式下手动刷新
    handleRefresh();
  }
}
</script>

<template>
  <div class="monitor-dashboard">
    <!-- 页面标题和控制区域 -->
    <div class="dashboard-header">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div class="header-content">
          <h1 class="dashboard-title">
            <Icon icon="mdi:monitor-dashboard" class="text-3xl" />
            监控仪表板
          </h1>
          <p class="dashboard-subtitle">实时监控系统状态、性能指标和业务数据</p>
        </div>

        <div class="header-controls">
          <div class="realtime-controls">
            <!-- 连接状态 -->
            <div class="connection-status">
              <div
                class="status-indicator"
                :class="{
                  '': isConnected,
                  'disconnected': !isConnected && !isConnecting,
                  'connecting': isConnecting
                }"
              ></div>
              <span>
                {{ isConnected ? '实时连接' : isConnecting ? '连接中...' : '连接断开' }}
              </span>
            </div>

            <!-- 实时模式切换 -->
            <div class="flex items-center gap-2">
              <span class="text-sm">实时模式</span>
              <NButton
                :type="realtimeMode ? 'primary' : 'default'"
                size="small"
                @click="toggleRealtimeMode"
                round
              >
                <template #icon>
                  <Icon :icon="realtimeMode ? 'mdi:wifi' : 'mdi:wifi-off'" />
                </template>
                {{ realtimeMode ? '开启' : '关闭' }}
              </NButton>
            </div>

            <!-- 手动刷新 -->
            <NButton @click="handleManualRefresh" :loading="loading || isConnecting" size="small" round>
              <template #icon>
                <Icon icon="mdi:refresh" />
              </template>
              {{ realtimeMode ? '重连' : '刷新' }}
            </NButton>

            <!-- 最后更新时间 -->
            <div v-if="lastUpdateTime" class="last-update">
              最后更新: {{ new Date(lastUpdateTime).toLocaleTimeString() }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <NSpin :show="loading">
      <div v-if="error" class="mb-4">
        <NAlert type="error" :title="error" closable @close="error = null" />
      </div>

      <!-- 主要监控卡片 -->
      <NGrid :cols="24" :x-gap="16" :y-gap="16">
        <!-- 系统状态概览 -->
        <NGridItem :span="24" :lg-span="12">
          <SystemOverviewCard
            :health-data="healthData"
            :system-data="systemData"
            :loading="loading"
          />
        </NGridItem>

        <!-- 任务执行概览 -->
        <NGridItem :span="24" :lg-span="12">
          <TaskOverviewCard
            :business-data="businessData"
            :loading="loading"
          />
        </NGridItem>

        <!-- 性能监控 -->
        <NGridItem :span="24" :lg-span="8">
          <PerformanceOverviewCard
            :performance-data="performanceData"
            :loading="loading"
          />
        </NGridItem>

        <!-- 资源监控 -->
        <NGridItem :span="24" :lg-span="8">
          <ResourceOverviewCard
            :system-data="systemData"
            :loading="loading"
          />
        </NGridItem>

        <!-- 错误分析 -->
        <NGridItem :span="24" :lg-span="8">
          <ErrorOverviewCard
            :loading="loading"
          />
        </NGridItem>
      </NGrid>
    </NSpin>

    <!-- 自动刷新提示 -->
    <div class="fixed bottom-4 right-4 text-xs text-gray-500 bg-white px-3 py-2 rounded-lg shadow-sm border">
      自动刷新: 每30秒
    </div>
  </div>
</template>

<style scoped>
@import './styles/overview-card.css';

.monitor-dashboard {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 64px);
  position: relative;
}

.monitor-dashboard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.monitor-dashboard > * {
  position: relative;
  z-index: 1;
}

.dashboard-header {
  margin-bottom: 32px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dashboard-title {
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.dashboard-subtitle {
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #10b981;
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.status-indicator.disconnected {
  background: #ef4444;
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
}

.status-indicator.connecting {
  background: #f59e0b;
  box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.realtime-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.last-update {
  font-size: 12px;
  color: #64748b;
  padding: 4px 8px;
  background: rgba(100, 116, 139, 0.1);
  border-radius: 8px;
}

.h-full {
  height: 100%;
}

.monitor-dashboard :deep(.overview-card) {
  height: 100%;
}

/* 全局卡片样式增强 */
:deep(.n-card) {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

:deep(.n-card:hover) {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

:deep(.n-card-header) {
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px 16px 0 0;
}

/* 网格间距优化 */
:deep(.n-grid) {
  gap: 20px;
}

/* 按钮样式增强 */
:deep(.n-button) {
  border-radius: 12px;
  transition: all 0.3s ease;
}

:deep(.n-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 标签样式增强 */
:deep(.n-tag) {
  border-radius: 8px;
  font-weight: 500;
}

/* 进度条样式增强 */
:deep(.n-progress .n-progress-graph .n-progress-graph-line-fill) {
  border-radius: 4px;
  transition: all 0.3s ease;
}

/* 统计数字动画 */
:deep(.n-statistic-value) {
  transition: all 0.3s ease;
}

:deep(.n-statistic:hover .n-statistic-value) {
  transform: scale(1.05);
  color: #3b82f6;
}

/* 图标动画 */
.iconify {
  transition: all 0.3s ease;
}

.iconify:hover {
  transform: rotate(5deg) scale(1.1);
}

/* 加载动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 卡片进入动画 */
:deep(.n-grid-item:nth-child(1)) {
  animation: slideInLeft 0.6s ease-out;
}

:deep(.n-grid-item:nth-child(2)) {
  animation: fadeIn 0.6s ease-out 0.1s both;
}

:deep(.n-grid-item:nth-child(3)) {
  animation: slideInRight 0.6s ease-out 0.2s both;
}

:deep(.n-grid-item:nth-child(4)) {
  animation: fadeIn 0.6s ease-out 0.3s both;
}

:deep(.n-grid-item:nth-child(5)) {
  animation: slideInLeft 0.6s ease-out 0.4s both;
}

/* 悬浮效果增强 */
:deep(.n-card) {
  position: relative;
  overflow: hidden;
}

:deep(.n-card::before) {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

:deep(.n-card:hover::before) {
  left: 100%;
}

/* 脉冲动画 */
@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.05);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(1);
  }
}

.status-indicator {
  animation: heartbeat 2s ease-in-out infinite;
}

/* 数据更新闪烁效果 */
@keyframes dataUpdate {
  0% {
    background-color: rgba(59, 130, 246, 0.1);
  }
  50% {
    background-color: rgba(59, 130, 246, 0.3);
  }
  100% {
    background-color: rgba(59, 130, 246, 0.1);
  }
}

.data-updated {
  animation: dataUpdate 1s ease-in-out;
}

/* 自动刷新提示样式 */
.fixed {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .monitor-dashboard {
    padding: 16px;
  }

  :deep(.n-grid) {
    gap: 16px;
  }
}

/* 深色主题适配 */
.dark .monitor-dashboard {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.dark .monitor-dashboard::before {
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
}

.dark .dashboard-header {
  background: rgba(30, 30, 30, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .connection-status {
  background: rgba(30, 30, 30, 0.7);
}

.dark .realtime-controls {
  background: rgba(30, 30, 30, 0.8);
}

.dark :deep(.n-card) {
  background: rgba(30, 30, 30, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark :deep(.n-card-header) {
  background: rgba(255, 255, 255, 0.02);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dark .fixed {
  background: rgba(30, 30, 30, 0.9) !important;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
}
</style>
