#!/usr/bin/env python3
import psutil
import os
import time

def test_psutil_directly():
    """直接测试psutil的CPU监控"""
    print("=== 直接测试psutil ===")

    # 获取当前进程
    current_process = psutil.Process(os.getpid())

    print(f"进程PID: {current_process.pid}")
    print(f"进程名称: {current_process.name()}")

    # 第一次调用cpu_percent()建立基准
    print("第一次调用cpu_percent()...")
    cpu1 = current_process.cpu_percent()
    print(f"第一次调用结果: {cpu1}%")

    # 等待一秒后再次调用
    print("等待1秒后再次调用...")
    time.sleep(1)
    cpu2 = current_process.cpu_percent()
    print(f"第二次调用结果: {cpu2}%")

    # 使用interval参数
    print("使用interval=0.1参数调用...")
    cpu3 = current_process.cpu_percent(interval=0.1)
    print(f"使用interval调用结果: {cpu3}%")

    # 系统整体CPU
    system_cpu = psutil.cpu_percent(interval=1)
    print(f"系统CPU使用率: {system_cpu}%")

def test_cpu_monitoring():
    """测试我们修复后的逻辑"""
    print("\n=== 测试修复后的逻辑 ===")

    # 模拟我们的修复逻辑
    try:
        import psutil
        import os

        # 创建全局进程对象
        _current_process = psutil.Process(os.getpid())
        # 初始化CPU监控（第一次调用建立基准）
        _current_process.cpu_percent()

        print("已初始化进程对象并建立基准")

        # 创建一些CPU活动
        print("创建CPU密集型任务...")
        start_time = time.time()
        result = 0
        while time.time() - start_time < 0.5:  # 运行0.5秒
            result += 1

        print(f"CPU密集型任务完成，计算了 {result} 次")

        # 获取CPU使用率（模拟我们的修复逻辑）
        app_cpu_percent = _current_process.cpu_percent()

        if app_cpu_percent == 0.0:
            print("第一次获取为0.0，使用interval=0.1重新获取...")
            app_cpu_percent = _current_process.cpu_percent(interval=0.1)

        print(f"应用程序CPU使用率: {app_cpu_percent}%")

        # 再次测试，这次在interval期间创建CPU活动
        print("\n在interval期间创建CPU活动...")
        def cpu_intensive_work():
            result = 0
            start = time.time()
            while time.time() - start < 0.05:  # 50ms的CPU密集型工作
                result += 1
            return result

        # 启动CPU密集型工作，然后立即测量
        import threading
        work_thread = threading.Thread(target=cpu_intensive_work)
        work_thread.start()

        app_cpu_percent2 = _current_process.cpu_percent(interval=0.1)
        work_thread.join()

        print(f"在CPU活动期间测量的CPU使用率: {app_cpu_percent2}%")

    except Exception as e:
        print(f"测试失败: {e}")
if __name__ == "__main__":
    test_psutil_directly()
    test_cpu_monitoring()
